/**
 * End-to-End Test: Account Settings User Profile Name Change Workflow
 *
 * This comprehensive test validates the complete user profile name editing workflow
 * in the account settings page, specifically focusing on updating the first name and
 * last name fields using the unique HTML element IDs with "user-profile-" prefix.
 *
 * SVELTEKIT PAGES TESTED:
 * - /settings/account (+page.svelte) - Account settings page with user profile management
 *   └── Loads user data via +page.server.ts load function
 *   └── Integrates UserProfile.svelte component for profile editing
 * - /users (+page.svelte) - Users management page for verification of changes
 *   └── Provides search functionality to verify updated user information
 *
 * SVELTE COMPONENTS TESTED:
 * - UserProfile.svelte (/src/lib/components/settings/account/UserProfile.svelte)
 *   └── Handles user profile form validation, submission, and success messaging
 *   └── First name input: settings-user-profile-first-name (line 465)
 *   └── Last name input: settings-user-profile-last-name (line 488)
 *   └── Save button: settings-user-profile-save-btn (line 403)
 *   └── Form container: settings-user-profile-form (line 390)
 *   └── Implements reactive form validation with button state management
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to account settings page (/settings/account)
 * 2. Capture original first name and last name values from form fields
 * 3. Update first name and last name with test values
 * 4. Verify form button state management (disabled/enabled based on changes)
 * 5. Submit form and wait for successful completion
 * 6. Verify UserProfile component displays updated names
 * 7. Navigate to Users page (/users) for cross-page verification
 * 8. Search for user using updated names and verify results
 * 9. Round-trip testing: revert changes to original values
 * 10. Verify restoration of original state across both pages
 *
 * DATA FLOW AND INTEGRATION POINTS:
 * - SvelteKit server-side data loading and form actions
 * - UserProfile component reactive form state management
 * - Cross-page data consistency verification
 * - Search functionality integration on Users page
 * - Real-time UI updates based on form state changes
 *
 * ROUND-TRIP TESTING PATTERN:
 * - Captures original state before any modifications
 * - Performs test operations and comprehensive verifications
 * - Reverts all changes to restore original state
 * - Ensures no test environment pollution
 * - Validates data consistency across multiple pages
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors use the "user-profile-" and "users-page-" prefix patterns
 * established in their respective Svelte components. Each selector references
 * actual HTML elements with documented line numbers for maintainability.
 * Language-agnostic DOM attribute assertions are used for robustness.
 */

import { test, expect } from '@playwright/test';
import type { Page } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

// Import authentication utilities
import { performLoginWithRedirectHandling } from './utils/auth.utils';

/**
 * Utility function to navigate to account settings page
 * COMPONENT: Settings navigation and account page
 * ROUTE: /settings/account
 */
async function navigateToAccountSettings(page: Page) {
	console.log('Navigating to account settings page...');
	
	// Navigate directly to account settings
	await page.goto('/settings/account');
	await page.waitForTimeout(1000);
	
	// Verify we're on the account settings page
	await expect(page).toHaveURL('/settings/account');
	
	// Wait for UserProfile component to load
	const userProfileForm = page.locator('#settings-user-profile-form');
	await expect(userProfileForm).toBeVisible({ timeout: 10000 });
	
	console.log('✓ Successfully navigated to account settings page');
}

/**
 * Utility function to capture original form values
 * COMPONENT: UserProfile.svelte
 * ELEMENTS: settings-user-profile-first-name, settings-user-profile-last-name
 */
async function captureOriginalValues(page: Page) {
	console.log('Capturing original form values...');
	
	const firstNameInput = page.locator('#settings-user-profile-first-name');
	const lastNameInput = page.locator('#settings-user-profile-last-name');
	
	await expect(firstNameInput).toBeVisible({ timeout: 10000 });
	await expect(lastNameInput).toBeVisible({ timeout: 10000 });
	
	const originalFirstName = await firstNameInput.inputValue();
	const originalLastName = await lastNameInput.inputValue();
	
	console.log(`Original first name: "${originalFirstName}"`);
	console.log(`Original last name: "${originalLastName}"`);
	
	return { originalFirstName, originalLastName };
}

/**
 * Utility function to update name fields with test values
 * COMPONENT: UserProfile.svelte
 * ELEMENTS: settings-user-profile-first-name, settings-user-profile-last-name
 */
async function updateNameFields(page: Page, testFirstName: string, testLastName: string) {
	console.log(`Updating names to: "${testFirstName}" "${testLastName}"`);
	
	const firstNameInput = page.locator('#settings-user-profile-first-name');
	const lastNameInput = page.locator('#settings-user-profile-last-name');
	const saveButton = page.locator('#settings-user-profile-save-btn');
	
	// Verify button is initially disabled (no changes)
	await expect(saveButton).toBeDisabled();
	console.log('✓ Save button initially disabled (no changes)');
	
	// Clear and fill first name
	await firstNameInput.clear();
	await firstNameInput.fill(testFirstName);
	await page.waitForTimeout(500);
	
	// Verify button becomes enabled after first change
	await expect(saveButton).toBeEnabled();
	console.log('✓ Save button enabled after first name change');
	
	// Clear and fill last name
	await lastNameInput.clear();
	await lastNameInput.fill(testLastName);
	await page.waitForTimeout(500);
	
	// Verify button remains enabled
	await expect(saveButton).toBeEnabled();
	console.log('✓ Save button remains enabled after last name change');
	
	// Verify input values
	await expect(firstNameInput).toHaveValue(testFirstName);
	await expect(lastNameInput).toHaveValue(testLastName);
	
	console.log('✓ Name fields updated successfully');
}

/**
 * Utility function to submit form and wait for completion
 * COMPONENT: UserProfile.svelte
 * ELEMENT: settings-user-profile-save-btn
 */
async function submitFormAndWait(page: Page) {
	console.log('Submitting form...');
	
	const saveButton = page.locator('#settings-user-profile-save-btn');
	await expect(saveButton).toBeEnabled();
	
	// Click save button
	await saveButton.click();
	await page.waitForTimeout(2000); // Wait for form submission
	
	// Verify button becomes disabled again (no pending changes)
	await expect(saveButton).toBeDisabled();
	console.log('✓ Form submitted successfully, save button disabled');
}

/**
 * Utility function to verify names in UserProfile component
 * COMPONENT: UserProfile.svelte
 * ELEMENTS: settings-user-profile-first-name, settings-user-profile-last-name
 */
async function verifyNamesInProfile(page: Page, expectedFirstName: string, expectedLastName: string) {
	console.log(`Verifying names in profile: "${expectedFirstName}" "${expectedLastName}"`);
	
	const firstNameInput = page.locator('#settings-user-profile-first-name');
	const lastNameInput = page.locator('#settings-user-profile-last-name');
	
	await expect(firstNameInput).toHaveValue(expectedFirstName);
	await expect(lastNameInput).toHaveValue(expectedLastName);
	
	console.log('✓ Names verified in UserProfile component');
}

/**
 * Utility function to search for user on Users page
 * COMPONENT: Users page (+page.svelte)
 * ELEMENT: users-page-search-input
 */
async function searchUserOnUsersPage(page: Page, searchTerm: string) {
	console.log(`Searching for user with term: "${searchTerm}"`);
	
	// Navigate to Users page
	await page.goto('/users');
	await page.waitForTimeout(1000);
	await expect(page).toHaveURL('/users');
	
	// Wait for users page to load
	const searchInput = page.locator('#users-page-search-input');
	await expect(searchInput).toBeVisible({ timeout: 10000 });
	
	// Perform search
	await searchInput.clear();
	await searchInput.fill(searchTerm);
	await page.waitForTimeout(1000); // Wait for search results
	
	// Verify search results contain at least one user
	const tableBody = page.locator('#users-page-table-body');
	await expect(tableBody).toBeVisible();
	
	// Check that we have search results (not the "no users" message)
	const noUsersRow = page.locator('#users-page-table-no-users-row');
	await expect(noUsersRow).not.toBeVisible();
	
	console.log('✓ User search completed with results');
}

test.describe('Account Settings User Profile Name Change', () => {
	test('should complete full user profile name change workflow with cross-page verification and reversion', async ({ page }) => {
		// Step 1: Authentication and navigation to account settings
		await performLoginWithRedirectHandling(page);
		await navigateToAccountSettings(page);
		
		// Step 2: Capture original values for round-trip testing
		const { originalFirstName, originalLastName } = await captureOriginalValues(page);
		
		// Step 3: Generate unique test names to avoid conflicts
		const testFirstName = `TestFirst${Date.now()}`;
		const testLastName = `TestLast${Date.now()}`;
		
		// Step 4: Update name fields and verify form state management
		await updateNameFields(page, testFirstName, testLastName);
		
		// Step 5: Submit form and wait for completion
		await submitFormAndWait(page);
		
		// Step 6: Verify UserProfile component displays updated names
		await verifyNamesInProfile(page, testFirstName, testLastName);
		
		// Step 7: Cross-page verification - search on Users page
		await searchUserOnUsersPage(page, testFirstName);
		await searchUserOnUsersPage(page, testLastName);
		
		// Step 8: Round-trip testing - revert to original values
		console.log('\n--- Performing round-trip testing ---');
		await navigateToAccountSettings(page);
		await updateNameFields(page, originalFirstName, originalLastName);
		await submitFormAndWait(page);
		
		// Step 9: Verify restoration of original values
		await verifyNamesInProfile(page, originalFirstName, originalLastName);
		
		// Step 10: Final verification on Users page
		await searchUserOnUsersPage(page, originalFirstName);
		
		console.log('✓ Round-trip testing completed - original state restored');
		console.log('\n🎉 Account settings user profile name change workflow completed successfully!');
	});
});
