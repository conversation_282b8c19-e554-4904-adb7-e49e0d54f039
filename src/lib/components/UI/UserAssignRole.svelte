<script lang="ts">
	import { enhance } from '$app/forms';
	import { <PERSON><PERSON>, Modal, Alert, Radio } from 'flowbite-svelte';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';

	export let user: any;
	export let roles: any[];
	export let onSuccess: () => void = () => {}; // Callback for successful operations

	let userAssignRoleForm: HTMLFormElement;
	let userAssignRoleModalOpen = false;
	let currentUser: any = null;
	let selectedRoleIds: number | null = null;
	let initialSelectedRoleIds: number | null = null; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when selection changes
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state (single value comparison for roles)
	$: hasChanges = selectedRoleIds !== initialSelectedRoleIds;

	const roleDict = {
		Admin: 1,
		System: 2,
		Supervisor: 3,
		Agent: 4
	};

	function openUserAssignRoleModal(user: any) {
		currentUser = { ...user };

		// console.log('currentUser', currentUser);
		// console.log('currentUser role id', currentUser.role.role_id);

		// Set default selected role to user's current role
		selectedRoleIds = currentUser.roles
			? roleDict[currentUser.roles as keyof typeof roleDict]
			: null;

		// Store initial state for change detection
		initialSelectedRoleIds = selectedRoleIds;

		userAssignRoleModalOpen = true;
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
	}

	function handleUserAssignRoleSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	$: enhanceOptions = {
		modalOpen: userAssignRoleModalOpen,
		setModalOpen: (value: boolean) => (userAssignRoleModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior - close modal and show toast
		useToastOnSuccess: true,
		closeModalOnSuccess: true,
		onSuccess: () => {
			// Call the original onSuccess if provided
			if (onSuccess) onSuccess();
		}
	};

	// Convert roles array to format expected by MultiSelect
	$: roleOptions = roles
		.filter((role) => role.role_id !== 1 && role.role_id !== 2)
		.map((role) => ({
			value: role.role_id,
			name: `${role.name}`
		}));


</script>

<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100"
	on:click={() => openUserAssignRoleModal(user)}>{t('user_assign_role')}</Button
>

<Modal bind:open={userAssignRoleModalOpen} size="md" autoclose={false} class="w-full">
	<h2 slot="header">{t('user_assign_role')}</h2>
	{#if currentUser}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}
		<form
			bind:this={userAssignRoleForm}
			action="?/assign_user_role"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleUserAssignRoleSubmit}
		>
			<input type="hidden" name="id" value={currentUser.id} />
			<div class="min-h-[200px]">
				<!-- <label for="SelectedRoles" class="mb-1 block text-left text-sm font-medium text-gray-700">
					Select User's Roles
				</label> -->
				<!-- <Select
					id="SelectedRoles"
					items={roleOptions}
					bind:value={selectedRoleIds}
					placeholder="Select roles..."
					class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
					selectClass="h-[200px] overflow-y-auto"
				/> -->
				<!-- {#each selectedRoleIds as roleId}
                    <input type="hidden" name="role_ids[]" value={roleId}>
                {/each} -->
				<input type="hidden" name="role_ids[]" value={selectedRoleIds} />

				{#each roleOptions as role (role.value)}
					<div class="mb-2">
						<Radio
							name="role"
							value={role.value}
							bind:group={selectedRoleIds}
							on:change={() => {
								dismissAlerts(); // Dismiss alerts when radio selection changes
							}}
						>
							{t(role.name.toLowerCase())}
							{#if currentUser.roles && selectedRoleIds === role.value}
								({t('current')})
							{/if}
						</Radio>
					</div>
				{/each}
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<Button color="blue" disabled={!hasChanges} on:click={() => userAssignRoleForm.requestSubmit()}
			>{t('confirm')}</Button
		>
		<Button color="none" on:click={() => (userAssignRoleModalOpen = false)}>{t('cancel')}</Button>
	</svelte:fragment>
</Modal>
