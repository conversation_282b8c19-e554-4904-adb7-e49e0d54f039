<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import { createEventDispatcher, onMount } from 'svelte';
	import {
		Card,
		Badge,
		Table,
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Button,
		Spinner,
		Indicator,
		Tooltip
	} from 'flowbite-svelte';
	import {
		TicketOutline,
		EditSolid,
		XSolid,
		UserCircleSolid,
		BriefcaseSolid
	} from 'flowbite-svelte-icons';
	import { 
		formatTimestamp,
		displayDate, 
		timeAgo, 
		getStatusClass, 
		getPriorityClass, 
		getSentimentClass, 
		getSentimentIcon,
		getColorClass,
		formatTime
	} from '$lib/utils';
	import { UserService } from '$lib/api/features/user/users.service';
	import { services } from '$lib/api/features';
	import LoadingSpinner from '$lib/components/common/LoadingSpinner.svelte';

	// Props
	export let isOpen = false;
	export let selectedUserId: number | null = null;
	export let token: string;

	// Event dispatcher
	const dispatch = createEventDispatcher();

	// Component state
	let isLoading = false;
	let error: string | null = null;
	let user: any = null;
	let userTickets: any[] = [];
	let partners: any[] = [];
	let departments: any[] = [];
	let tags: any[] = [];

	// Services
	const userService = new UserService();

	// Status configuration
	const statusConfig = {
		online: { color: 'green', label: 'Online' },
		offline: { color: 'red', label: 'Offline' },
		away: { color: 'yellow', label: 'Away' }
	};

	// Reactive statements
	$: userStatusInfo = user ? (statusConfig[user.status] || {color: 'gray', label: 'Unknown'}) : null;
	$: hasUserImage = user && user.image_url && user.image_url.length > 0;

	// Watch for selectedUserId changes
	$: if (selectedUserId && isOpen) {
		fetchUserDetails();
	}

	// Focus management
	$: if (isOpen) {
		// Focus the close button when sidebar opens
		setTimeout(() => {
			const closeButton = document.getElementById('user-detail-sidebar-close-button');
			if (closeButton) {
				closeButton.focus();
			}
		}, 100);
	}

	// Functions
	function closeSidebar() {
		dispatch('close');
	}

	function handleKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			closeSidebar();
		}
	}

	async function fetchUserDetails() {
		if (!selectedUserId || !token) return;
		
		isLoading = true;
		error = null;
		
		try {
			// Fetch user details
			const userResponse = await services.users.getById(selectedUserId.toString(), token);
			if (userResponse.res_status === 200) {
				user = userResponse.users;
			} else {
				throw new Error(userResponse.error_msg || 'Failed to fetch user details');
			}

			// Fetch user tickets
			const ticketsResponse = await services.users.getUserTickets(selectedUserId.toString(), token);
			if (ticketsResponse.res_status === 200) {
				userTickets = ticketsResponse.users?.tickets || [];
			}

			// Fetch related data for display
			const [partnersResponse, departmentsResponse, tagsResponse] = await Promise.all([
				services.companies.getAll(token),
				services.departments.getAll(token),
				services.users.getAllTags(token)
			]);

			partners = partnersResponse.partners || [];
			departments = departmentsResponse.departments || [];
			tags = tagsResponse.tags || [];

		} catch (err) {
			console.error('Error fetching user details:', err);
			error = err instanceof Error ? err.message : 'Failed to load user details';
		} finally {
			isLoading = false;
		}
	}

	function getUserWorkSchedule(user: any) {
		if (!user?.work_schedule?.schedule?.workShift) {
			return null;
		}

		const workShift = user.work_schedule.schedule.workShift;
		const scheduleDisplay: Record<string, string> = {};

		workShift.forEach((dayData: any) => {
			const dayKey = `day_${dayData.day.toLowerCase()}`;
			
			if (!dayData.active || !dayData.times || dayData.times.length === 0) {
				scheduleDisplay[dayKey] = 'off';
			} else {
				const timeRanges = dayData.times.map((timeSlot: any) => {
					const startTime = formatTime(timeSlot.start);
					const endTime = formatTime(timeSlot.end);
					return `${startTime} - ${endTime}`;
				});
				
				scheduleDisplay[dayKey] = timeRanges.join(', ');
			}
		});

		return {
			workShift: workShift,
			scheduleDisplay: scheduleDisplay,
			isBusinessHours: user.work_schedule.same_as_business_hours
		};
	}

	// Lifecycle
	onMount(() => {
		if (typeof window !== 'undefined') {
			window.addEventListener('keydown', handleKeydown);
			return () => {
				window.removeEventListener('keydown', handleKeydown);
			};
		}
	});
</script>

<!-- Sidebar Container -->
<div
	id="user-detail-sidebar-container"
	class="fixed inset-y-0 right-0 z-50 w-full md:w-1/2 transform bg-white shadow-xl transition-transform duration-300 ease-in-out {isOpen ? 'translate-x-0' : 'translate-x-full'}"
	role="dialog"
	aria-modal="true"
	aria-labelledby="user-detail-sidebar-title"
>
	<!-- Header -->
	<div id="user-detail-sidebar-header" class="flex items-center justify-between border-b border-gray-200 p-4">
		<h2 id="user-detail-sidebar-title" class="text-lg font-semibold text-gray-900">
			{t('user_details')}
		</h2>
		<Button
			id="user-detail-sidebar-close-button"
			color="none"
			size="sm"
			class="p-2 hover:bg-gray-100"
			on:click={closeSidebar}
		>
			<XSolid class="h-5 w-5" />
		</Button>
	</div>

	<!-- Content -->
	<div id="user-detail-sidebar-content" class="flex-1 overflow-y-auto p-4">
		{#if isLoading}
			<div id="user-detail-sidebar-loading" class="flex h-64 items-center justify-center">
				<LoadingSpinner size="lg" />
			</div>
		{:else if error}
			<div id="user-detail-sidebar-error" class="rounded-lg bg-red-50 p-4">
				<p class="text-sm text-red-600">{error}</p>
				<Button
					id="user-detail-sidebar-retry-button"
					color="red"
					size="sm"
					class="mt-2"
					on:click={fetchUserDetails}
				>
					{t('retry')}
				</Button>
			</div>
		{:else if user}
			<!-- User Header -->
			<div id="user-detail-sidebar-user-header" class="mb-6 flex items-start">
				<div class="relative mr-4">
					<div class="h-16 w-16 overflow-hidden rounded-full bg-gray-100 relative">
						{#if hasUserImage}
							<img src="{user.image_url}" alt="{user.first_name} {user.last_name}" class="h-full w-full object-cover" />
						{:else}
							<div class="h-full w-full flex items-center justify-center bg-gray-300 text-gray-700 font-medium text-xl">
								{user.first_name ? user.first_name[0] : ''}{user.last_name ? user.last_name[0] : ''}
							</div>
						{/if}
					</div>
					
					<!-- Status indicator -->
					{#if userStatusInfo}
						<div
							class="absolute bottom-0 right-0 h-4 w-4 rounded-full border-2 border-white bg-{userStatusInfo.color}-500"
							aria-label={userStatusInfo.label}
							title={userStatusInfo.label}
						></div>
					{/if}
				</div>
				<div class="flex-1">
					<h3 id="user-detail-sidebar-user-name" class="text-lg font-bold text-gray-900 truncate">
						{user.first_name} {user.last_name} ({user.name})
					</h3>
					<div class="flex items-center gap-2 mt-2">
						{#if user.status === 'online'}
							<Badge color="green">{t('online')}</Badge>
						{:else if user.status === 'away'}
							<Badge color="yellow">{t('away')}</Badge>
						{:else if user.status === 'offline'}
							<Badge color="red">{t('offline')}</Badge>
						{:else}
							<Badge color="red">{t('offline')}</Badge>
						{/if}
						<Badge color="blue">{t((user.roles).toLowerCase())}</Badge>
					</div>
				</div>
			</div>

			<!-- Profile Section -->
			<div id="user-detail-sidebar-profile-section" class="mb-6">
				<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
					<div class="border-b border-gray-200 p-4">
						<div class="flex items-center gap-2">
							<UserCircleSolid class="h-5 w-5 text-blue-600" />
							<h3 class="text-lg font-medium text-gray-700">{t('user_profile')}</h3>
						</div>
					</div>
					<div class="p-4">
						<div class="grid grid-cols-1 gap-y-3">
							<div>
								<div class="text-sm text-gray-500">{t('user_number')}</div>
								<div class="text-sm">{user.id}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('first_name')}</div>
								<div class="text-sm">{user.first_name || '-'}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('last_name')}</div>
								<div class="text-sm">{user.last_name || '-'}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('username')}</div>
								<div class="text-sm">{user.username || '-'}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('email')}</div>
								<div class="text-sm">{user.email}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('last_active')}</div>
								<div class="text-sm">
									{#if user.last_active}
										{displayDate(user.last_active).date} {displayDate(user.last_active).time}
									{:else}
										-
									{/if}
								</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('status')}</div>
								<div class="text-sm">{t(user.status)}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('is_active')}</div>
								<div class="text-sm">
									{#if user.is_active}
										<span class="text-green-600 font-medium">{t('enabled')}</span>
									{:else}
										<span class="text-red-600 font-medium">{t('suspended')}</span>
									{/if}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- Work Information Section -->
			<div id="user-detail-sidebar-work-section" class="mb-6">
				<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
					<div class="border-b border-gray-200 p-4">
						<div class="flex items-center gap-2">
							<BriefcaseSolid class="h-5 w-5 text-blue-600" />
							<h3 class="text-lg font-medium text-gray-700">{t('work_information')}</h3>
						</div>
					</div>
					<div class="p-4">
						<div class="grid grid-cols-1 gap-y-3">
							<div>
								<div class="text-sm text-gray-500">{t('role')}</div>
								<div class="text-sm">{t(user.roles.toLowerCase())}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('current_workload')}</div>
								<div class="text-sm">{userTickets.filter(ticket => ticket.status === 'assigned').length}</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('partner')}</div>
								<div class="text-sm">
									{#if user.partners && user.partners.length}
										{#each user.partners as partner}
											<span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
												<Indicator size="sm" class={`mr-1 ${getColorClass(partner.color)} inline-block`} />
												{partner.name}
											</span>
										{/each}
									{:else}
										{t('no_partners')}
									{/if}
								</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('department')}</div>
								<div class="text-sm">
									{#if user.departments && user.departments.length}
										{#each user.departments as department}
											<span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
												<Indicator size="sm" class={`mr-1 ${getColorClass(department.color)} inline-block`} />
												{department.name}
											</span>
										{/each}
									{:else}
										{t('no_departments')}
									{/if}
								</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('specialized_tags')}</div>
								<div class="text-sm">
									{#if user.tags && user.tags.length}
										{#each user.tags as tag}
											<span class="text-white-700 mb-1 mr-1 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
												<Indicator size="sm" class={`mr-1 ${getColorClass(tag.color)} inline-block`} />
												{tag.name}
											</span>
										{/each}
									{:else}
										{t('no_specialize_tags')}
									{/if}
								</div>
							</div>
							<div>
								<div class="text-sm text-gray-500">{t('work_shift_column')}</div>
								<div class="text-sm">
									{#if user.work_schedule}
										{@const workSchedule = getUserWorkSchedule(user)}
										{#if workSchedule}
											<div class="space-y-1 text-sm">
												{#each Object.entries(workSchedule.scheduleDisplay) as [dayKey, time]}
													<div class="flex min-w-[200px] flex-col">
														<div class="flex items-start justify-between">
															<span class="font-medium text-gray-900 min-w-[80px]">{t(dayKey)}:</span>
															<div class="text-right text-gray-700 flex-1 ml-2">
																{#if time === 'off'}
																	<span class="text-gray-500">{t('off')}</span>
																{:else if typeof time === 'string' && time.includes(', ')}
																	{#each time.split(', ') as timeSlot}
																		<div class="leading-tight">
																			{timeSlot}
																		</div>
																	{/each}
																{:else}
																	{time}
																{/if}
															</div>
														</div>
													</div>
												{/each}
											</div>
										{:else}
											{t('no_schedule_set')}
										{/if}
									{:else}
										{t('no_schedule_set')}
									{/if}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Tickets Section -->
			<div id="user-detail-sidebar-tickets-section" class="mb-6">
				<div class="rounded-lg border border-gray-200 bg-white shadow-sm">
					<div class="border-b border-gray-200 p-4">
						<div class="flex items-center gap-2">
							<TicketOutline class="h-5 w-5 text-blue-600" />
							<h3 class="text-lg font-medium text-gray-700">{t('my_tasks')}</h3>
							<span class="ml-auto rounded-full bg-gray-100 px-2 py-1 text-sm font-medium text-gray-600">
								{userTickets.filter(ticket => ticket.status === 'assigned').length}
							</span>
						</div>
					</div>
					<div class="w-full">
						{#if userTickets && userTickets.length > 0}
							<Table>
								<TableHead>
									<TableHeadCell>{t('table_no')}</TableHeadCell>
									<TableHeadCell>{t('table_status')}</TableHeadCell>
									<TableHeadCell>{t('table_priority')}</TableHeadCell>
									<TableHeadCell>{t('table_sentiment')}</TableHeadCell>
									<TableHeadCell>{t('table_customer')}</TableHeadCell>
									<TableHeadCell>{t('table_time')}</TableHeadCell>
									<TableHeadCell>{t('table_updated_on')}</TableHeadCell>
								</TableHead>
								<TableBody>
									{#each userTickets as ticket}
										<TableBodyRow>
											<TableBodyCell>
												<a
													href="/monitoring/{ticket.id}"
													class="flex items-center justify-start text-blue-600 hover:underline py-2"
												>
													{ticket.id}<EditSolid class="h-4 w-4" />
												</a>
											</TableBodyCell>
											<TableBodyCell>
												<div class="flex justify-start">
													<span class={`${getStatusClass(ticket.status_id)} px-3 py-1 rounded-md text-sm w-32 text-center`}>
														{ticket.status.charAt(0).toUpperCase() + ticket.status.slice(1)}
													</span>
												</div>
											</TableBodyCell>
											<TableBodyCell>
												<div class="flex justify-start">
													<span class={`${getPriorityClass(ticket.priority.name)} p-2 rounded-md text-sm w-24`}>
														{ticket.priority.name ?? "-"}
													</span>
												</div>
											</TableBodyCell>
											<TableBodyCell>
												<div class="flex justify-center">
													<div class={`flex items-center justify-center gap-1 rounded-md p-2 ${getSentimentClass(ticket.latest_analysis?.sentiment)}`}>
														<img
															src={getSentimentIcon(ticket.latest_analysis?.sentiment)}
															alt={ticket.latest_analysis?.sentiment}
															class="w-5 h-5"
														/>
														<Tooltip>{ticket.latest_analysis?.sentiment ?? 'Unclassified'}</Tooltip>
													</div>
												</div>
											</TableBodyCell>
											<TableBodyCell>
												{ticket.customer.name
													? ticket.customer.name
													: ticket.customer.line_user.display_name}
											</TableBodyCell>
											<TableBodyCell>{timeAgo(ticket.updated_on)}</TableBodyCell>
											<TableBodyCell>
												<div class="text-sm">{displayDate(ticket.updated_on).date}</div>
												<div class="text-xs text-gray-500">{displayDate(ticket.updated_on).time}</div>
											</TableBodyCell>
										</TableBodyRow>
									{/each}
								</TableBody>
							</Table>
						{:else}
							<div class="p-6 text-center text-gray-500">
								{t('no_tasks_assigned')}
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/if}
	</div>
</div>
