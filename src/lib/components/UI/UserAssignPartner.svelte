<script lang="ts">
	import { enhance } from '$app/forms';
	import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Checkbox } from 'flowbite-svelte';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { t } from '$src/lib/stores/i18n';

	export let user: any;
	export let partners: any[];

	let userAssignPartnerForm: HTMLFormElement;
	let userAssignPartnerModalOpen = false;
	let currentUser: any = null;
	let selectedPartnerIds: (string | number)[] = [];
	let initialSelectedPartnerIds: (string | number)[] = []; // Track initial state for change detection

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';
	let fieldErrors: Record<string, string[]> = {}; // Multi-field error support

	// Function to parse error messages (supports both single string and multi-field errors)
	function parseErrorMessages(error: any): {
		fieldErrors: Record<string, string[]>;
		generalError: string | null;
	} {
		if (!error) return { fieldErrors: {}, generalError: null };

		let errorObj = error;

		// Handle string errors - check if it's a JSON string first
		if (typeof error === 'string') {
			// Try to parse as JSON first
			try {
				errorObj = JSON.parse(error);
			} catch (e) {
				// If JSON parsing fails, treat as plain string (backward compatibility)
				return { fieldErrors: {}, generalError: error };
			}
		}

		// Handle object errors with field-specific messages
		if (typeof errorObj === 'object' && errorObj !== null) {
			const fieldErrors: Record<string, string[]> = {};
			let hasFieldErrors = false;

			for (const [fieldName, fieldErrorArray] of Object.entries(errorObj)) {
				if (Array.isArray(fieldErrorArray)) {
					const validErrors = fieldErrorArray.filter((msg: any) => typeof msg === 'string');
					if (validErrors.length > 0) {
						fieldErrors[fieldName] = validErrors;
						hasFieldErrors = true;
					}
				}
			}

			if (hasFieldErrors) {
				return { fieldErrors, generalError: null };
			}
		}

		return { fieldErrors: {}, generalError: 'An error occurred' };
	}

	// Function to dismiss alerts when selection changes
	function dismissAlerts() {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {};
	}

	// Reactive statement to detect changes from initial state
	$: hasChanges =
		JSON.stringify([...selectedPartnerIds].sort()) !==
		JSON.stringify([...initialSelectedPartnerIds].sort());

	// TODO: Remove
	// console.log(`Current User:${currentUser.partners}`)

	function openUserAssignPartnerModal(user: any) {
		currentUser = { ...user };

		if (currentUser.partners && Array.isArray(currentUser.partners)) {
			selectedPartnerIds = currentUser.partners
				.map((partner: any) => (typeof partner === 'object' ? partner.id : partner))
				// Only keep values that are valid numbers (or numeric strings)
				.filter((id: any) => id !== undefined && id !== null && !isNaN(Number(id)));
		} else {
			selectedPartnerIds = [];
		}

		// Store initial state for change detection
		initialSelectedPartnerIds = [...selectedPartnerIds];

		userAssignPartnerModalOpen = true;
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
		fieldErrors = {}; // Reset field errors
	}

	// function openUserAssignPartnerModal(user: any) {
	//     currentUser = { ...user };

	//     // Initialize selectedPartnerIds based on current user's assigned partners
	//     if (currentUser.partners && Array.isArray(currentUser.partners)) {
	//         selectedPartnerIds = currentUser.partners.map(partner =>
	//             // If partner is an object, get the `id`; else assume it's already the `id`
	//             typeof partner === 'object' ? partner.id : partner
	//         );
	//     } else {
	//         selectedPartnerIds = []; // If no partners are found, initialize as empty
	//     }

	//     userAssignPartnerModalOpen = true;
	//     showSuccessMessage = false;
	//     showErrorMessage = false;
	//     successMessage = '';
	//     errorMessage = '';
	// }

	function handleUserAssignPartnerSubmit(_event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	$: enhanceOptions = {
		modalOpen: userAssignPartnerModalOpen,
		setModalOpen: (value: boolean) => (userAssignPartnerModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => {
			const parsedResult = parseErrorMessages(value);
			fieldErrors = parsedResult.fieldErrors;
			errorMessage = parsedResult.generalError || '';
			// Set showErrorMessage to true when we have either field errors or general error
			showErrorMessage =
				Object.keys(parsedResult.fieldErrors).length > 0 || parsedResult.generalError !== null;
		},
		// Enhanced success behavior - close modal and show toast
		useToastOnSuccess: true,
		closeModalOnSuccess: true
	};

	// Convert partners array to format expected by checkboxes
	$: partnerOptions = partners.map((partner) => ({
		value: partner.id,
		name: `${partner.name} (${partner.code || 'null'})` // Handle missing partner code gracefully
	}));
</script>

<Button
	color="none"
	class="w-full justify-start p-2 text-left hover:bg-gray-100"
	on:click={() => openUserAssignPartnerModal(user)}
>
	{t('user_assign_partner')}
</Button>

<Modal bind:open={userAssignPartnerModalOpen} size="md" autoclose={false} class="w-full">
	<h2 slot="header">{t('user_assign_partner')}</h2>
	{#if currentUser}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<!-- Field-specific error display for multi-field errors -->
		{#if Object.keys(fieldErrors).length > 0}
			{#each Object.entries(fieldErrors) as [fieldName, errors]}
				{#each errors as error}
					<Alert color="red" class="mb-4">
						<strong>{fieldName}:</strong>
						{error}
					</Alert>
				{/each}
			{/each}
		{/if}
		<form
			bind:this={userAssignPartnerForm}
			action="?/assign_user_partner"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handleUserAssignPartnerSubmit}
		>
			<input type="hidden" name="id" value={currentUser.id} />
			<div class="min-h-[200px]">
				<!-- <label
					for="SelectedPartners"
					class="mb-1 block text-left text-sm font-medium text-gray-700"
				>
					Select User's Partners
				</label> -->
				{#each partnerOptions as partner (partner.value)}
					<div class="mb-2">
						<Checkbox
							checked={selectedPartnerIds.includes(partner.value)}
							value={partner.value}
							on:change={() => {
								// Handle checkbox state change
								if (selectedPartnerIds.includes(partner.value)) {
									selectedPartnerIds = selectedPartnerIds.filter((id) => id !== partner.value);
								} else {
									selectedPartnerIds = [...selectedPartnerIds, partner.value];
								}
								dismissAlerts(); // Dismiss alerts when checkbox selection changes
							}}
						>
							{partner.name}
						</Checkbox>
					</div>
				{/each}
				<input type="hidden" name="partner_ids[]" value={selectedPartnerIds} />
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<Button
			color="blue"
			disabled={!hasChanges}
			on:click={() => userAssignPartnerForm.requestSubmit()}>{t('confirm')}</Button
		>
		<Button color="none" on:click={() => (userAssignPartnerModalOpen = false)}>{t('cancel')}</Button
		>
	</svelte:fragment>
</Modal>
