<!-- +page.svelte - Enhanced Real-time with Better Change Detection -->
<script lang="ts">
	import { t, language } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { get } from 'svelte/store';
	import { onMount, onDestroy } from 'svelte';

	import {
		TableBody,
		TableBodyCell,
		TableBodyRow,
		TableHead,
		TableHeadCell,
		Table,
		Button,
		Dropdown,
		Checkbox,
		Spinner,
		Indicator,
		Avatar,
		Badge,
		Input,
		Tooltip
	} from 'flowbite-svelte';
	import {
		EditSolid,
		ChevronDownOutline,
		UserHeadsetSolid,
		AdjustmentsHorizontalSolid,
		UserCircleSolid,
		SearchOutline,
		ClockOutline
	} from 'flowbite-svelte-icons';
	import { getColorClass } from '$lib/utils';

	import { CaretDownSolid, CaretUpSolid } from 'flowbite-svelte-icons';
	import { formatTimestamp, displayDate, timeAgo, getUserWorkSchedule } from '$lib/utils';
	import type { PageData } from './$types';
	import UserSignUp from '$lib/components/UI/UserSignUp.svelte';
	import Pagination from '$src/lib/components/UI/pagination.svelte';
	import AdminStatistics from '$src/lib/components/admin/AdminStatistics.svelte';
	import { UserService } from '$lib/api/features/user/users.service';
	import { Breadcrumb, BreadcrumbItem } from 'flowbite-svelte';
	import UserDetailSidebar from '$lib/components/UI/UserDetailSidebar.svelte';

	export let data: PageData;
	$: ({
		// users: initialUsers,
		all_users,
		statuses,
		roles,
		partners,
		departments,
		error,
		all_tags,
		token
	} = data);

	// Add this reactive statement to see when all_users changes
	// $: {
	// 	console.log('ALL USERS (reactive):', all_users);
	// }

	interface User {
		id: number;
		roles: string;
		partners: string;
		username: string;
		email: string;
		status: string;
		is_active: boolean;
		name: string;
		employee_id: number;
		current_workload: number;
		last_active: string;
	}

	// ============== ENHANCED REAL-TIME UPDATE VARIABLES ==============
	let isRealTimeEnabled = true;
	let pollingInterval = 5000; // 5 seconds default
	let pollingIntervalId = null;
	let forceRefreshIntervalId = null; // Force refresh every 30 seconds
	let lastUpdateTime = new Date();
	let lastForceRefresh = new Date();
	let isTabVisible = true;
	let hasNewUpdates = false;
	let updateIndicatorTimeout = null;
	let connectionStatus = 'connected';
	let retryCount = 0;
	let maxRetries = 3;
	let baseRetryDelay = 5000;
	let recentlyChangedUsers = new Set();
	let changeIndicatorTimeout = 5000;
	let adaptivePolling = true;
	let basePollingInterval = 5000;
	let fastPollingInterval = 3000;
	let slowPollingInterval = 30000;
	let userInteractionTimeout;
	let isPaused = false;
	let updateCounter = 0; // Track number of updates
	let userDataSnapshot = new Map(); // Store user data snapshots for better comparison
	let debugMode = false; // Enable for debugging

	// ============== FILTER VARIABLES ==============
	let selectedStatuses = new Set(['All']);
	let selectedRoles = new Set(['All']);
	let selectedPartners = new Set(['All']);
	let selectedDepartments = new Set(['All']);
	let selectedSpecializedTags = new Set(['All']);
	let selectedActiveStatus = 'All';

	// Available options
	const statusOptions = ['All', 'online', 'busy', 'away', 'offline'];
	const roleOptions = ['All', 'Admin', 'System', 'Supervisor', 'Agent'];
	const activeStatusOptions = ['All', 'Active', 'Inactive'];

	$: partnerOptions = [
		{ name: 'All', code: 'All' },
		...Array.from(
			new Map(
				(partners || [])
					.filter(
						(partner: any) =>
							partner.name && partner.name !== '' && partner.name.toLowerCase() !== 'all'
					)
					.map((partner: any) => [
						`${partner.name}-${partner.code}`,
						{ name: partner.name, code: partner.code }
					])
			).values()
		).sort((a: any, b: any) => a.name.localeCompare(b.name))
	];

	$: departmentOptions = [
		{ name: 'All', code: 'All' },
		...Array.from(
			new Map(
				(departments || [])
					.filter((dept: any) => dept.name && dept.name !== '')
					.map((dept: any) => [`${dept.name}-${dept.code}`, { name: dept.name, code: dept.code }])
			).values()
		).sort((a: any, b: any) => a.name.localeCompare(b.name))
	];

	$: specializedTagOptions = [
		'All',
		...Array.from(
			new Set(
				(all_tags || []).map((tag: any) => tag.name).filter((name: string) => name && name !== '')
			)
		).sort((a: string, b: string) => a.localeCompare(b))
	];

	$: partnerCounts = {};
	$: departmentCounts = {};
	$: specializedTagCounts = {};

	$: {
		partnerCounts = {};
		departmentCounts = {};
		specializedTagCounts = {};

		for (const user of users) {
			for (const partner of user.partners || []) {
				const name = partner.name;
				if (name !== '' && name !== 'all') {
					partnerCounts[name] = (partnerCounts[name] || 0) + 1;
				}
			}

			for (const dept of user.departments || []) {
				const name = dept.name;
				if (name !== '' && name !== 'all') {
					departmentCounts[name] = (departmentCounts[name] || 0) + 1;
				}
			}

			for (const tag of user.user_tags || []) {
				const name = tag.name;
				if (name !== '' && name !== 'all') {
					specializedTagCounts[name] = (specializedTagCounts[name] || 0) + 1;
				}
			}
		}
	}

	// ============== DATA VARIABLES ==============
	let users: any[] = [];
	const userService = new UserService();
	let isLoading = false;

	// ============== PAGINATION VARIABLES ==============
	let currentPage = 1;
	let itemsPerPage = 10;
	let totalItems = 0;
	let totalPages = 1;

	// ============== SORTING VARIABLES ==============
	let sortColumn = 'id';
	let sortDirection = 'asc';
	let currentOrdering = 'id';

	// ============== SEARCH VARIABLES ==============
	let searchQuery = '';
	let searchTimeout: ReturnType<typeof setTimeout>;

	// ============== SIDEBAR VARIABLES ==============
	let isSidebarOpen = false;
	let selectedUserId: number | null = null;

	// ============== SERVER-SIDE ORDERING MAPPING ==============
	const columnFieldMap = {
		id: 'id',
		status: 'status',
		workload: 'current_workload',
		name: 'first_name',
		last_active: 'last_active'
	};

	// ============== ENHANCED REAL-TIME FUNCTIONS ==============
	function handleVisibilityChange() {
		isTabVisible = !document.hidden;
		
		if (isTabVisible && isRealTimeEnabled) {
			connectionStatus = 'connected';
			retryCount = 0;
			startPolling();
			startForceRefresh();
			// Immediately fetch when tab becomes visible
			fetchUsersRealTime(true); // Force refresh when tab becomes visible
		} else {
			stopPolling();
			stopForceRefresh();
		}
	}

	function startPolling() {
		if (pollingIntervalId) {
			clearInterval(pollingIntervalId);
		}
		
		if (isRealTimeEnabled && isTabVisible && !isPaused) {
			pollingIntervalId = setInterval(() => {
				if (!isPaused) {
					fetchUsersRealTime();
				}
			}, pollingInterval);
		}
	}

	function stopPolling() {
		if (pollingIntervalId) {
			clearInterval(pollingIntervalId);
			pollingIntervalId = null;
		}
	}

	// Force refresh every 30 seconds to ensure data sync
	function startForceRefresh() {
		if (forceRefreshIntervalId) {
			clearInterval(forceRefreshIntervalId);
		}
		
		if (isRealTimeEnabled && isTabVisible) {
			forceRefreshIntervalId = setInterval(() => {
				if (debugMode) console.log('🔄 Force refresh triggered');
				fetchUsersRealTime(true); // Force refresh
				lastForceRefresh = new Date();
			}, 30000); // Every 30 seconds
		}
	}

	function stopForceRefresh() {
		if (forceRefreshIntervalId) {
			clearInterval(forceRefreshIntervalId);
			forceRefreshIntervalId = null;
		}
	}

	function toggleRealTime() {
		isRealTimeEnabled = !isRealTimeEnabled;
		
		if (isRealTimeEnabled) {
			startPolling();
			startForceRefresh();
			fetchUsersRealTime(true);
		} else {
			stopPolling();
			stopForceRefresh();
		}
	}

	// Enhanced real-time fetch with better change detection
	async function fetchUsersRealTime(forceRefresh = false) {
		if (isLoading && !forceRefresh) return;
		
		const previousUsers = [...users];
		const previousSnapshot = new Map(userDataSnapshot);
		
		try {
			connectionStatus = 'connected';
			retryCount = 0;
			
			await fetchUsers(currentOrdering, true);
			updateCounter++;
			
			// Create new snapshot for comparison
			updateUserSnapshot();
			
			// Enhanced change detection
			const changedUsers = detectEnhancedUserChanges(previousUsers, users, previousSnapshot);
			
			if (adaptivePolling && !forceRefresh) {
				adjustPollingInterval();
			}
			
			// More comprehensive change detection
			const hasChanges = changedUsers.size > 0 || hasStructuralChanges(previousUsers, users);
			
			if (hasChanges || forceRefresh) {
				hasNewUpdates = true;
				lastUpdateTime = new Date();
				recentlyChangedUsers = changedUsers;
				
				if (debugMode) {
					console.log(`🔄 Update #${updateCounter} - Changes detected:`, {
						changedUsers: Array.from(changedUsers),
						totalUsers: users.length,
						previousUsers: previousUsers.length,
						forceRefresh
					});
				}
				
				if (updateIndicatorTimeout) {
					clearTimeout(updateIndicatorTimeout);
				}
				updateIndicatorTimeout = setTimeout(() => {
					hasNewUpdates = false;
					recentlyChangedUsers.clear();
				}, 3000);
			}
			
		} catch (error) {
			console.error('Real-time update failed:', error);
			connectionStatus = 'reconnecting';
			
			if (retryCount < maxRetries) {
				retryCount++;
				const retryDelay = baseRetryDelay * Math.pow(2, retryCount - 1);
				
				setTimeout(() => {
					if (isRealTimeEnabled && isTabVisible) {
						fetchUsersRealTime(true); // Force refresh on retry
					}
				}, retryDelay);
			} else {
				connectionStatus = 'disconnected';
			}
		}
	}

	// Enhanced change detection with better comparison
	function detectEnhancedUserChanges(oldUsers, newUsers, previousSnapshot) {
		const changedUserIds = new Set();
		
		// Create maps for easier comparison
		const oldUserMap = new Map(oldUsers.map(u => [u.id, u]));
		const newUserMap = new Map(newUsers.map(u => [u.id, u]));
		
		// Check for changes in existing users
		for (const [userId, newUser] of newUserMap) {
			const oldUser = oldUserMap.get(userId);
			const previousData = previousSnapshot.get(userId);
			
			if (!oldUser || hasComprehensiveUserChanged(oldUser, newUser, previousData)) {
				changedUserIds.add(userId);
			}
		}
		
		// Check for removed users
		for (const [userId] of oldUserMap) {
			if (!newUserMap.has(userId)) {
				if (debugMode) console.log(`👤 User ${userId} removed`);
			}
		}
		
		// Check for new users
		for (const [userId] of newUserMap) {
			if (!oldUserMap.has(userId)) {
				changedUserIds.add(userId);
				if (debugMode) console.log(`👤 New user ${userId} added`);
			}
		}
		
		return changedUserIds;
	}

	// More comprehensive user change detection
	function hasComprehensiveUserChanged(oldUser, newUser, previousData) {
		if (!oldUser || !newUser) return true;
		
		// Check all important fields that might change
		const fieldsToCheck = [
			'status',
			'current_workload', 
			'last_active',
			'is_active',
			'first_name',
			'last_name',
			'username',
			'email',
			'roles'
		];
		
		for (const field of fieldsToCheck) {
			if (oldUser[field] !== newUser[field]) {
				if (debugMode) {
					console.log(`🔄 User ${newUser.id} ${field} changed:`, {
						old: oldUser[field],
						new: newUser[field]
					});
				}
				return true;
			}
		}
		
		// Check array fields (partners, departments, tags)
		if (hasArrayFieldChanged(oldUser.partners, newUser.partners) ||
		    hasArrayFieldChanged(oldUser.departments, newUser.departments) ||
		    hasArrayFieldChanged(oldUser.user_tags, newUser.user_tags)) {
			return true;
		}
		
		// Compare with previous snapshot if available
		if (previousData) {
			for (const field of fieldsToCheck) {
				if (previousData[field] !== newUser[field]) {
					if (debugMode) {
						console.log(`🔄 User ${newUser.id} ${field} changed from snapshot:`, {
							snapshot: previousData[field],
							current: newUser[field]
						});
					}
					return true;
				}
			}
		}
		
		return false;
	}

	// Check if array fields have changed
	function hasArrayFieldChanged(oldArray, newArray) {
		if (!oldArray && !newArray) return false;
		if (!oldArray || !newArray) return true;
		if (oldArray.length !== newArray.length) return true;
		
		// Compare array contents
		for (let i = 0; i < oldArray.length; i++) {
			const oldItem = oldArray[i];
			const newItem = newArray[i];
			
			if (typeof oldItem === 'object' && typeof newItem === 'object') {
				if (JSON.stringify(oldItem) !== JSON.stringify(newItem)) {
					return true;
				}
			} else if (oldItem !== newItem) {
				return true;
			}
		}
		
		return false;
	}

	// Check for structural changes (user count, order)
	function hasStructuralChanges(oldUsers, newUsers) {
		if (oldUsers.length !== newUsers.length) {
			if (debugMode) console.log(`📊 User count changed: ${oldUsers.length} → ${newUsers.length}`);
			return true;
		}
		
		// Check if user order changed (might indicate sorting/filtering changes)
		for (let i = 0; i < oldUsers.length; i++) {
			if (oldUsers[i]?.id !== newUsers[i]?.id) {
				if (debugMode) console.log(`📋 User order changed at position ${i}`);
				return true;
			}
		}
		
		return false;
	}

	// Update user data snapshot for comparison
	function updateUserSnapshot() {
		userDataSnapshot.clear();
		for (const user of users) {
			userDataSnapshot.set(user.id, {
				status: user.status,
				current_workload: user.current_workload,
				last_active: user.last_active,
				is_active: user.is_active,
				first_name: user.first_name,
				last_name: user.last_name,
				username: user.username,
				email: user.email,
				roles: user.roles
			});
		}
	}

	// Legacy functions kept for compatibility
	function hasDataChanged(oldUsers, newUsers) {
		return hasStructuralChanges(oldUsers, newUsers) || 
		       detectEnhancedUserChanges(oldUsers, newUsers, userDataSnapshot).size > 0;
	}

	function adjustPollingInterval() {
		const now = Date.now();
		const timeSinceLastUpdate = now - lastUpdateTime.getTime();
		
		let newInterval;
		if (timeSinceLastUpdate < 30000) {
			newInterval = fastPollingInterval;
		} else if (timeSinceLastUpdate > 120000) {
			newInterval = slowPollingInterval;
		} else {
			newInterval = basePollingInterval;
		}
		
		if (newInterval !== pollingInterval) {
			pollingInterval = newInterval;
			stopPolling();
			startPolling();
		}
	}

	function pauseRealTimeTemporarily() {
		if (!isRealTimeEnabled) return;
		
		isPaused = true;
		
		if (userInteractionTimeout) {
			clearTimeout(userInteractionTimeout);
		}
		
		userInteractionTimeout = setTimeout(() => {
			isPaused = false;
		}, 15000);
	}

	// ============== DATA FETCHING FUNCTIONS ==============
	async function fetchUsers(ordering = 'id', isRealTimeCall = false) {
		if (isLoading && !isRealTimeCall) return;
		
		if (!isRealTimeCall) {
			isLoading = true;
		}

		const statusFilters = Array.from(selectedStatuses).filter((s) => s !== 'All');
		const roleFilters = Array.from(selectedRoles).filter((r) => r !== 'All');
		const partnerFilters = Array.from(selectedPartners).filter((p) => p !== 'All');
		const departmentFilters = Array.from(selectedDepartments).filter((d) => d !== 'All');
		const specializedTagFilters = Array.from(selectedSpecializedTags).filter((t) => t !== 'All');

		const filters = {
			search: searchQuery.trim() || '',
			status: statusFilters.join(','),
			role: roleFilters.join(','),
			partner: partnerFilters.join(','),
			department: departmentFilters.join(','),
			user_tag: specializedTagFilters.join(','),
			page: currentPage,
			page_size: itemsPerPage,
			// Add timestamp to prevent caching issues
			_t: Date.now()
		};

		try {
			const response = await userService.getUsersWithFiltersAndOrdering(token, filters, ordering);

			if (response.res_status === 200) {
				if (response.users?.results) {
					users = response.users.results;
					const backendPageSize = 10;
					const totalCount = response.users.count || 0;
					totalPages = Math.ceil(totalCount / backendPageSize);
					totalItems = totalCount;
				} else {
					users = response.users || [];
					totalItems = users.length;
					totalPages = 1;
				}
				
				// Update snapshot on successful fetch
				if (isRealTimeCall) {
					updateUserSnapshot();
				}
			} else {
				console.error('Failed to fetch users:', response.error_msg);
				users = [];
				totalItems = 0;
				totalPages = 1;
			}
		} catch (error) {
			console.error('Failed to fetch users:', error.message);
			users = [];
			totalItems = 0;
			totalPages = 1;
			
			if (isRealTimeCall) {
				throw error;
			}
		}

		if (!isRealTimeCall) {
			isLoading = false;
		}
	}

	// ============== FILTER FUNCTIONS ==============
	function toggleStatus(status: string) {
		const newSet = new Set(selectedStatuses);

		if (status === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(status)) {
				newSet.delete(status);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(status);
			}
		}

		selectedStatuses = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleRole(role: string) {
		const newSet = new Set(selectedRoles);

		if (role === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(role)) {
				newSet.delete(role);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(role);
			}
		}

		selectedRoles = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function togglePartner(partner: { name: string; code: string }) {
		const newSet = new Set(selectedPartners);
		const partnerName = partner.name;

		if (partnerName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(partnerName)) {
				newSet.delete(partnerName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(partnerName);
			}
		}

		selectedPartners = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleDepartment(department: { name: string; code: string }) {
		const newSet = new Set(selectedDepartments);
		const departmentName = department.name;

		if (departmentName === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(departmentName)) {
				newSet.delete(departmentName);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(departmentName);
			}
		}

		selectedDepartments = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function toggleSpecializedTag(tag: string) {
		const newSet = new Set(selectedSpecializedTags);

		if (tag === 'All') {
			newSet.clear();
			newSet.add('All');
		} else {
			newSet.delete('All');
			if (newSet.has(tag)) {
				newSet.delete(tag);
				if (newSet.size === 0) {
					newSet.add('All');
				}
			} else {
				newSet.add(tag);
			}
		}

		selectedSpecializedTags = newSet;
		currentPage = 1;
		fetchUsers(currentOrdering);
	}

	function resetFilters() {
		selectedStatuses = new Set(['All']);
		selectedRoles = new Set(['All']);
		selectedPartners = new Set(['All']);
		selectedDepartments = new Set(['All']);
		selectedSpecializedTags = new Set(['All']);
		selectedActiveStatus = 'All';
		sortColumn = 'id';
		sortDirection = 'asc';
		searchQuery = '';
		currentPage = 1;
		currentOrdering = 'id';
		fetchUsers('id');
	}

	// ============== SEARCH FUNCTIONS ==============
	function delayedSearch() {
		if (searchTimeout) clearTimeout(searchTimeout);
		searchTimeout = setTimeout(() => {
			currentPage = 1;
			fetchUsers();
		}, 500);
	}

	$: searchQuery, delayedSearch();

	// ============== SORTING FUNCTIONS ==============
	function sortBy(column: string) {
		if (sortColumn === column) {
			sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
		} else {
			sortColumn = column;
			sortDirection = 'asc';
		}
		handleServerSort(column);
	}

	function handleServerSort(column: string) {
		const field = columnFieldMap[column];
		if (field) {
			const ordering = sortDirection === 'desc' ? `-${field}` : field;
			currentOrdering = ordering;
			currentPage = 1;
			fetchUsers(ordering);
		}
	}

	// ============== PAGINATION FUNCTIONS ==============
	$: totalPages = Math.ceil(totalItems / itemsPerPage) || 1;
	$: paginatedUsers = users;

	function updateCurrentPage(newCurrentPage: number) {
		currentPage = newCurrentPage;
		fetchUsers(currentOrdering);
	}

	// ============== SIDEBAR FUNCTIONS ==============
	function openSidebar(userId: number) {
		selectedUserId = userId;
		isSidebarOpen = true;
		// Pause real-time updates when sidebar is open to avoid conflicts
		pauseRealTimeTemporarily();
	}

	async function closeSidebar() {
		isSidebarOpen = false;
		selectedUserId = null;
		
		// Refresh the users table when sidebar closes to ensure data is up to date
		await fetchUsers(currentOrdering);
		console.log('Users table refreshed after sidebar close');
	}

	// Handle user updates from sidebar
	async function handleUserUpdated(event) {
		console.log('User updated event received:', event.detail);
		
		// Add a small delay to ensure server data is fully updated
		await new Promise(resolve => setTimeout(resolve, 300));
		
		// Refresh the users table data
		await fetchUsers(currentOrdering);
		
		console.log('Users table refreshed after user update');
	}

	function handleUserRowClick(userId: number) {
		openSidebar(userId);
	}

	// ============== UTILITY FUNCTIONS ==============
	function formatTagName(tag: string): string {
		return tag.charAt(0).toUpperCase() + tag.slice(1).split('_').join(' ');
	}

	$: userMatchedTags = users?.map((user) => ({
		id: user.id,
		tags: user.user_tags || []
	}));

	// ============== DEBUG FUNCTIONS ==============
	function toggleDebugMode() {
		debugMode = !debugMode;
		console.log(`🐛 Debug mode ${debugMode ? 'enabled' : 'disabled'}`);
	}

	function forceRefreshNow() {
		console.log('🔄 Manual force refresh triggered');
		fetchUsersRealTime(true);
	}

	// ============== LIFECYCLE ==============
	onMount(() => {
		fetchUsers();
		updateUserSnapshot(); // Initial snapshot
		
		if (isRealTimeEnabled) {
			startPolling();
			startForceRefresh();
		}
		
		if (typeof document !== 'undefined') {
			document.addEventListener('visibilitychange', handleVisibilityChange);
			
			['click', 'scroll', 'keypress', 'touchstart'].forEach(eventType => {
				document.addEventListener(eventType, pauseRealTimeTemporarily, { passive: true });
			});
		}
	});

	onDestroy(() => {
		stopPolling();
		stopForceRefresh();
		
		if (typeof document !== 'undefined') {
			document.removeEventListener('visibilitychange', handleVisibilityChange);
			
			['click', 'scroll', 'keypress', 'touchstart'].forEach(eventType => {
				document.removeEventListener(eventType, pauseRealTimeTemporarily);
			});
		}
		
		if (updateIndicatorTimeout) {
			clearTimeout(updateIndicatorTimeout);
		}
		
		if (userInteractionTimeout) {
			clearTimeout(userInteractionTimeout);
		}
	});
</script>

<svelte:head>
	<title>{t('users')}</title>
</svelte:head>

<div id="users-page-container" class="relative flex h-screen">
	<!-- Overlay for dimming effect when sidebar is open -->
	{#if isSidebarOpen}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div
			id="users-page-overlay"
			class="fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300"
			on:click={closeSidebar}
		></div>
	{/if}

	<div id="users-page-main" class="w-full overflow-y-auto bg-white p-8 transition-all duration-300 {isSidebarOpen ? 'md:mr-1/2' : ''}">
		<Breadcrumb id="users-page-breadcrumb" aria-label="Default breadcrumb example" class="mb-3">
			<BreadcrumbItem id="users-page-breadcrumb-home" href="/" home>
				<span class="text-gray-400">{t('home')}</span>
			</BreadcrumbItem>
			<BreadcrumbItem id="users-page-breadcrumb-users">
				<span class="text-gray-700">{t('users')}</span>
			</BreadcrumbItem>
		</Breadcrumb>

		<!-- Enhanced Header with Real-time Status -->
		<div id="users-page-header" class="mb-6 flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
			<div class="flex-1">
				<div class="flex items-center gap-4 mb-2">
					<h2 id="users-page-title" class="text-2xl font-bold">{t('users_page_title')}</h2>
					
					<!-- Enhanced Real-time Status Indicator -->
					<!-- {#if isRealTimeEnabled}
						<div class="flex items-center gap-2 text-sm">
							<div class={`h-2 w-2 rounded-full ${
								connectionStatus === 'connected' ? 'bg-green-500 animate-pulse' : 
								connectionStatus === 'reconnecting' ? 'bg-yellow-500 animate-pulse' : 
								'bg-red-500'
							}`}></div>
							<span class="text-gray-600">
								{connectionStatus === 'connected' ? `⚡ Live (${pollingInterval/1000}s)` : 
								 connectionStatus === 'reconnecting' ? `🔄 Reconnecting (${retryCount}/${maxRetries})` : 
								 '❌ Disconnected'}
							</span>
							{#if isLoading}
								<Spinner class="w-4 h-4" />
							{/if}
							{#if updateCounter > 0}
								<span class="text-xs text-blue-600">#{updateCounter}</span>
							{/if}
						</div>
					{/if} -->
				</div>
				
				<div class="flex items-center gap-4">
					<p class="text-gray-600">{t('users_page_description')}</p>
					
					<!-- {#if isRealTimeEnabled}
						<div class="flex items-center gap-2 text-xs text-gray-500">
							<ClockOutline class="h-3 w-3" />
							<span>Last: {lastUpdateTime.toLocaleTimeString()}</span>
							{#if adaptivePolling}
								<span class="text-blue-600">Smart: {pollingInterval / 1000}s</span>
							{:else}
								<span>Every: {pollingInterval / 1000}s</span>
							{/if}
							{#if recentlyChangedUsers.size > 0}
								<span class="text-green-600 font-medium">⚡ {recentlyChangedUsers.size} live updates</span>
							{/if}
							<span class="text-xs text-purple-600">Force: {lastForceRefresh.toLocaleTimeString()}</span>
						</div>
					{/if} -->
				</div>
			</div>
			
			{#if data.role === 'Admin'}
				<div class="flex flex-nowrap gap-2 overflow-x-auto md:overflow-visible">
					<UserSignUp id="users-page-add-user-signup" count={users?.length || 0} />
				</div>
			{/if}
		</div>

		<AdminStatistics id="users-page-admin-statistics" users={all_users} />

		<!-- Enhanced Filters with Real-time Controls -->
		<div id="users-page-filters" class="mb-6 flex flex-col items-start gap-4 lg:flex-row lg:items-center lg:justify-between">
			<!-- Left side - Filter Buttons -->
			<div id="users-page-filter-buttons" class="flex flex-wrap gap-3">
				<!-- Status Filter -->
				<div>
					<Button
						id="users-page-status-filter-button"
						color={!selectedStatuses.has('All') ? 'dark' : 'none'}
						class={`${!selectedStatuses.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_status')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown id="users-page-status-filter-dropdown" class="w-44 p-2 shadow-lg">
						{#each statusOptions as status}
							<div class="rounded p-2 hover:bg-gray-100">
								<Checkbox
									id="users-page-status-filter-{status.toLowerCase()}"
									checked={selectedStatuses.has(status)}
									on:change={() => toggleStatus(status)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{status === 'All'
											? 'All Status'
											: status.charAt(0).toUpperCase() + status.slice(1)}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Role Filter -->
				<div>
					<Button
						id="users-page-role-filter-button"
						color={!selectedRoles.has('All') ? 'dark' : 'none'}
						class={`${!selectedRoles.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_role')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown id="users-page-role-filter-dropdown" class="w-44 p-2 shadow-lg">
						{#each roleOptions as role}
							<div class="rounded p-2 hover:bg-gray-100">
								<Checkbox
									id="users-page-role-filter-{role.toLowerCase()}"
									checked={selectedRoles.has(role)}
									on:change={() => toggleRole(role)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">{role}</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Partner Filter -->
				<div>
					<Button
						id="users-page-partner-filter-button"
						color={!selectedPartners.has('All') ? 'dark' : 'none'}
						class={`${!selectedPartners.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_partner')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown id="users-page-partner-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each partnerOptions as partner}
							<div class="flex items-center justify-between rounded p-2 hover:bg-gray-100">
								<Checkbox
									id="users-page-partner-filter-{partner.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}"
									checked={selectedPartners.has(partner.name)}
									on:change={() => togglePartner(partner)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{#if partner.name === 'All'}
											{partner.name}
										{:else}
											{formatTagName(partner.name)} ({partner.code})
										{/if}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Department Filter -->
				<div>
					<Button
						id="users-page-department-filter-button"
						color={!selectedDepartments.has('All') ? 'dark' : 'none'}
						class={`${!selectedDepartments.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_department')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown id="users-page-department-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each departmentOptions as department}
							<div class="flex items-center justify-between rounded p-2 hover:bg-gray-100">
								<Checkbox
									id="users-page-department-filter-{department.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}"
									checked={selectedDepartments.has(department.name)}
									on:change={() => toggleDepartment(department)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{#if department.name === 'All'}
											{department.name}
										{:else}
											{formatTagName(department.name)} ({department.code})
										{/if}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Specialized Tag Filter -->
				<div>
					<Button
						id="users-page-specialized-tag-filter-button"
						color={!selectedSpecializedTags.has('All') ? 'dark' : 'none'}
						class={`${!selectedSpecializedTags.has('All') ? '' : 'border hover:bg-gray-100'} shadow-md`}
					>
						<AdjustmentsHorizontalSolid class="h-4 w-4" />
						<span>{t('filter_specialized_tag')}</span>
						<ChevronDownOutline class="h-3 w-3" />
					</Button>
					<Dropdown id="users-page-specialized-tag-filter-dropdown" class="max-h-80 w-72 overflow-y-auto p-2 shadow-lg">
						{#each specializedTagOptions as tag}
							<div class="flex items-center justify-between rounded p-2 hover:bg-gray-100">
								<Checkbox
									id="users-page-specialized-tag-filter-{tag.toLowerCase().replace(/[^a-z0-9]/g, '-')}"
									checked={selectedSpecializedTags.has(tag)}
									on:change={() => toggleSpecializedTag(tag)}
									class="flex items-center"
								>
									<span class="ml-2 text-sm">
										{tag === 'All' ? tag : formatTagName(tag)}
									</span>
								</Checkbox>
							</div>
						{/each}
					</Dropdown>
				</div>

				<!-- Reset Filter -->
				<Button
					id="users-page-reset-filters-button"
					color="none"
					on:click={resetFilters}
					class="w-auto border shadow-md hover:bg-gray-100"
				>
					{t('filter_reset')}
				</Button>

				<!-- Real-time Controls Separator -->
				<!-- <div class="border-l border-gray-300 mx-2"></div> -->

				<!-- Enhanced Real-time Toggle -->
				<!-- <div class="flex items-center gap-2">
					<Button
						color={isRealTimeEnabled ? (connectionStatus === 'connected' ? 'green' : connectionStatus === 'reconnecting' ? 'yellow' : 'red') : 'none'}
						size="sm"
						on:click={toggleRealTime}
						class={`${isRealTimeEnabled ? '' : 'border hover:bg-gray-100'} shadow-md transition-all duration-200 relative ${
							isRealTimeEnabled && pollingInterval <= 5000 ? 'animate-pulse' : ''
						}`}
					>
						<div class={`absolute -top-1 -right-1 h-3 w-3 rounded-full ${
							connectionStatus === 'connected' ? 'bg-green-500 animate-pulse' : 
							connectionStatus === 'reconnecting' ? 'bg-yellow-500 animate-bounce' : 
							'bg-red-500'
						}`}></div>
						
						<svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
							{#if isRealTimeEnabled}
								<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
							{:else}
								<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
							{/if}
						</svg>
						
						{isRealTimeEnabled ? 
							(pollingInterval <= 5000 ? '⚡ Live' : 'Live') : 'Manual'}
					</Button>
					

					{#if hasNewUpdates}
						<div class="flex items-center gap-1 text-green-600">
							<div class="h-2 w-2 bg-green-500 rounded-full"></div>
							<span class="text-xs font-medium">
								{recentlyChangedUsers.size > 0 ? `${recentlyChangedUsers.size} updated` : 'Updated'}
							</span>
						</div>
					{/if}
					
					{#if isPaused && isRealTimeEnabled}
						<div class="flex items-center gap-1 text-yellow-600">
							<svg class="h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
								<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 002 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
							</svg>
							<span class="text-xs">Paused</span>
						</div>
					{/if}
				</div> -->

				<!-- {#if isRealTimeEnabled}
					<div class="flex items-center gap-2">
						<span class="text-sm text-gray-600">
							{adaptivePolling ? 'Smart:' : 'Every:'}
						</span>
						<select
							bind:value={pollingInterval}
							on:change={() => {
								stopPolling();
								startPolling();
							}}
							class="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
							disabled={adaptivePolling}
						>
							<option value={3000}>3s</option>
							<option value={5000}>5s</option>
							<option value={10000}>10s</option>
							<option value={30000}>30s</option>
							<option value={60000}>1m</option>
							<option value={120000}>2m</option>
							<option value={300000}>5m</option>
						</select>
						
						<label class="flex items-center gap-1 text-xs cursor-pointer">
							<input
								type="checkbox"
								bind:checked={adaptivePolling}
								class="w-3 h-3"
							/>
							<span class="text-gray-600">Auto</span>
						</label>
					</div>
				{/if} -->

				<!-- {#if typeof window !== 'undefined' && window.location.hostname === 'localhost'}
					<div class="flex items-center gap-1">
						<Button
							color="none"
							size="sm"
							on:click={toggleDebugMode}
							class={`text-xs border ${debugMode ? 'bg-yellow-100' : ''}`}
						>
							
						</Button>
						<Button
							color="none"
							size="sm"
							on:click={forceRefreshNow}
							class="text-xs border"
						>
							
						</Button>
					</div>
				{/if} -->
			</div>

			<!-- Right side - Search Bar -->
			<div id="users-page-search-container" class="relative w-full shadow-md lg:w-1/3">
				<div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
					<SearchOutline class="h-5 w-5 text-gray-500" />
				</div>
				<Input
					id="users-page-search-input"
					type="text"
					placeholder={t('search_user_placeholder')}
					bind:value={searchQuery}
					class={`block w-full rounded-lg border bg-white py-2.5 pl-10
                        focus:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700
                        ${searchQuery ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'}`}
				/>
			</div>
		</div>

		<!-- Enhanced Table with Better Change Indicators -->
		<Table id="users-page-table" shadow class="w-full table-fixed">
			<TableHead id="users-page-table-head">
				<TableHeadCell id="users-page-table-header-id" class="w-[60px]" on:click={() => sortBy('id')}>
					<div class="flex items-center justify-center">
						{t('table_no')}
						{#if sortColumn === 'id'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-status" class="w-[90px]" on:click={() => sortBy('status')}>
					<div class="flex items-center justify-center">
						{t('table_status')}
						{#if sortColumn === 'status'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-workload" class="w-[110px]" on:click={() => sortBy('workload')}>
					<div class="flex items-center justify-center">
						{t('table_workload')}
						{#if sortColumn === 'workload'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-name" class="w-[200px]" on:click={() => sortBy('name')}>
					<div class="flex items-center justify-start">
						{t('table_name')}
						{#if sortColumn === 'name'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
				<TableHeadCell id="users-page-table-header-role" class="w-[100px]">{t('table_role')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-partner" class="w-[110px]">{t('table_partner')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-department" class="w-[110px]">{t('table_department')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-specialized-tag" class="w-[110px]">{t('table_specialize_tag')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-work-shift" class="w-[80px] text-center">{t('work_shift_column')}</TableHeadCell>
				<TableHeadCell id="users-page-table-header-last-active" class="w-[150px]" on:click={() => sortBy('last_active')}>
					<div class="flex items-center justify-start">
						{t('table_last_active')}
						{#if sortColumn === 'last_active'}
							{#if sortDirection === 'desc'}
								<CaretDownSolid class="ml-1 h-4 w-4" />
							{:else}
								<CaretUpSolid class="ml-1 h-4 w-4" />
							{/if}
						{/if}
					</div>
				</TableHeadCell>
			</TableHead>
			<TableBody id="users-page-table-body">
				{#if paginatedUsers.length === 0}
					<TableBodyRow id="users-page-table-no-users-row">
						<TableBodyCell id="users-page-table-no-users-cell" colspan={10} class="py-4 text-center text-gray-500">
							{t('no_users')}
						</TableBodyCell>
					</TableBodyRow>
				{:else}
					{#each paginatedUsers as user}
						<!-- svelte-ignore a11y-click-events-have-key-events -->
						<!-- svelte-ignore a11y-no-static-element-interactions -->
						<TableBodyRow
							id="users-page-table-row-{user.id}"
							class="cursor-pointer hover:bg-gray-50 transition-colors duration-150"
							on:click={() => handleUserRowClick(user.id)}
						>
							
							<TableBodyCell id="users-page-table-cell-id-{user.id}">
								<span
									id="users-page-user-{user.id}"
									class="flex items-center justify-center py-2"
								>
									{user.id}
								</span>
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-status-{user.id}">
								<span id="users-page-user-status-{user.id}" class={`flex rounded-md px-2 py-1 text-sm text-center justify-center ${
									user.status === 'online' ? 'bg-green-200 text-green-700' :
									user.status === 'busy' ? 'bg-red-200 text-red-700' :
									user.status === 'away' ? 'bg-yellow-200 text-yellow-700' :
									'bg-gray-100 text-gray-700'
								}`}>
									{t(user.status)}
								</span>
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-workload-{user.id}">
								<span id="users-page-user-workload-{user.id}" class="flex justify-center items-center">{user.current_workload}</span>
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-name-{user.id}">
								<span id="users-page-user-name-{user.id}" class="break-words">{user.first_name} {user.last_name}</span>
								<div class="truncate text-xs text-gray-500">{user.username}</div>
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-role-{user.id}">
								<span id="users-page-user-role-{user.id}">{t(user.roles.toLowerCase()) ? t(user.roles.toLowerCase()) : '-'}</span>
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-partner-{user.id}">
								{#if user.partners && user.partners.length > 0}
									{#if user.partners.length === 1}
										<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
											<Indicator
												size="sm"
												class={`mr-1 ${getColorClass(user.partners[0].color)} inline-block`}
											/>
											{user.partners[0].code}
										</span>
									{:else}
										<div class="relative inline-block">
											<span
												class="text-white-700 inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
												data-popover-target="popover-partners-{user.id}"
											>
												<span class="flex items-center gap-1">
													<span class="relative flex -space-x-1">
														{#each user.partners.slice(0, 3) as partners, i (partners.name)}
															<Indicator
																size="sm"
																class={`${getColorClass(partners.color)}`}
																style="z-index: {10 - i};"
															/>
														{/each}
													</span>
													{user.partners.length}
													{t('labels')}
												</span>
											</span>

											<Tooltip triggeredBy="[data-popover-target='popover-partners-{user.id}']">
												<div class="max-w-xs px-2 py-1">
													<ul class="space-y-1">
														{#each user.partners as partner}
															<li class="flex items-center gap-1">
																<Indicator
																	size="sm"
																	class={`mr-1 ${getColorClass(partner.color)}`}
																/>
																{partner.code}
															</li>
														{/each}
													</ul>
												</div>
											</Tooltip>
										</div>
									{/if}
								{:else}
									-
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-department-{user.id}">
								{#if user.departments && user.departments.length > 0}
									{#if user.departments.length === 1}
										<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
											<Indicator
												size="sm"
												class={`mr-1 ${getColorClass(user.departments[0].color)} inline-block`}
											/>
											{formatTagName(user.departments[0].code)}
										</span>
									{:else}
										<div class="relative inline-block">
											<span
												class="text-white-700 inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
												data-popover-target="popover-departments-{user.id}"
											>
												<span class="flex items-center gap-1">
													<span class="relative flex -space-x-1">
														{#each user.departments.slice(0, 3) as department, i (department.name)}
															<Indicator
																size="sm"
																class={`${getColorClass(department.color)}`}
																style="z-index: {10 - i};"
															/>
														{/each}
													</span>
													{user.departments.length}
													{t('labels')}
												</span>
											</span>

											<Tooltip triggeredBy="[data-popover-target='popover-departments-{user.id}']">
												<div class="max-w-xs px-2 py-1">
													<ul class="space-y-1">
														{#each user.departments as department}
															<li class="flex items-center gap-1">
																<Indicator
																	size="sm"
																	class={`mr-1 ${getColorClass(department.color)}`}
																/>
																{formatTagName(department.code)}
															</li>
														{/each}
													</ul>
												</div>
											</Tooltip>
										</div>
									{/if}
								{:else}
									-
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-specialized-tag-{user.id}">
								{#if user.user_tags && user.user_tags.length > 0}
									{#each userMatchedTags.filter((ut) => ut.id === user.id) as matched}
										{#if matched.tags && matched.tags.length > 0}
											{#if matched.tags.length === 1}
												<span class="text-white-700 inline-block rounded-md bg-gray-100 px-2 py-1 text-sm">
													<Indicator
														size="sm"
														class={`mr-1 ${getColorClass(matched.tags[0].color)} inline-block`}
													/>
													{formatTagName(matched.tags[0].name)}
												</span>
											{:else}
												<div class="relative inline-block">
													<span
														class="inline-flex cursor-pointer items-center gap-1 rounded-md bg-gray-100 px-2 py-1 text-sm"
														data-popover-target="popover-tags-{user.id}"
													>
														<span class="flex items-center gap-1">
															<span class="relative flex -space-x-1">
																{#each matched.tags.slice(0, 3) as tag, i (tag.name)}
																	<Indicator
																		size="sm"
																		class={`${getColorClass(tag.color)}`}
																		style="z-index: {10 - i};"
																	/>
																{/each}
															</span>
															{matched.tags.length}
															{t('labels')}
														</span>
													</span>

													<Tooltip triggeredBy="[data-popover-target='popover-tags-{user.id}']">
														<div class="max-w-xs px-2 py-1">
															<ul class="space-y-1">
																{#each matched.tags as tag}
																	<li class="flex items-center gap-1">
																		<Indicator
																			size="sm"
																			class={`mr-1 ${getColorClass(tag.color)}`}
																		/>
																		{formatTagName(tag.name)}
																	</li>
																{/each}
															</ul>
														</div>
													</Tooltip>
												</div>
											{/if}
										{:else}
											<span class="italic text-gray-500">{t('no_tags')}</span>
										{/if}
									{/each}
								{:else}
									-
								{/if}
							</TableBodyCell>
			  				
			  				<TableBodyCell id="users-page-table-cell-work-shift-{user.id}">
								{@const workSchedule = getUserWorkSchedule(user)}
								<div class="flex justify-center items-center gap-2">
									{#if workSchedule}
										<ClockOutline
											class="h-4 w-4 text-gray-500"
											data-popover-target="popover-workshift-{user.id}"
										/>
									{:else}
										<span class="text-gray-400 text-sm">{t('no_schedule_set')}</span>
									{/if}
								</div>

								{#if workSchedule}
									<Tooltip
										triggeredBy="[data-popover-target='popover-workshift-{user.id}']"
										class="z-50 border border-gray-200 bg-white shadow-lg"
										placement="bottom"
									>
										<div class="px-3 py-2">
											<!-- <div class="mb-2 font-semibold text-gray-900">
												{workSchedule.isBusinessHours ? t('business_hours') : t('weekly_schedule')}
											</div> -->
											<div class="space-y-2 text-sm">
												{#each Object.entries(workSchedule.scheduleDisplay) as [dayKey, time]}
													<div class="flex min-w-[200px] flex-col">
														<div class="flex items-start justify-between">
															<span class="font-medium text-gray-900 min-w-[80px]">{t(dayKey)}:</span>
															<div class="text-right text-gray-700 flex-1 ml-2">
																{#if time === 'off'}
																	<span class="text-gray-500">{t('off')}</span>
																{:else if time.includes(', ')}
																	{#each time.split(', ') as timeSlot, i}
																		<div class="leading-tight">
																			{timeSlot}
																		</div>
																	{/each}
																{:else}
																	{time}
																{/if}
															</div>
														</div>
													</div>
												{/each}
											</div>
										</div>
									</Tooltip>
								{/if}
							</TableBodyCell>
							
							<TableBodyCell id="users-page-table-cell-last-active-{user.id}">
								<div id="users-page-user-last-active-{user.id}">
									<div>{displayDate(user.last_active).date}</div>
									<div>{displayDate(user.last_active).time}</div>
								</div>
							</TableBodyCell>
						</TableBodyRow>
					{/each}
				{/if}
			</TableBody>
		</Table>

		<!-- Pagination -->
		<Pagination {currentPage} {totalPages} visibleCount={10} {updateCurrentPage} />

		<!-- Enhanced Real-time Statistics -->
		<!-- {#if isRealTimeEnabled}
			<div class="text-xs text-gray-500 flex flex-wrap items-center gap-4 mt-4 p-3 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border">
				<span class="font-medium text-blue-700">⚡ Live Dashboard:</span>
				<span>Last: {lastUpdateTime.toLocaleTimeString()}</span>
				<span class="font-medium text-blue-600">Every: {pollingInterval / 1000}s</span>
				<span class="text-purple-600">Force: {lastForceRefresh.toLocaleTimeString()}</span>
				{#if adaptivePolling}
					<span class="text-blue-600 bg-blue-100 px-2 py-1 rounded">🧠 Smart Mode</span>
				{/if}
				{#if recentlyChangedUsers.size > 0}
					<span class="text-green-600 font-medium bg-green-100 px-2 py-1 rounded">🔥 {recentlyChangedUsers.size} live changes</span>
				{/if}
				<span class={`font-medium px-2 py-1 rounded ${
					connectionStatus === 'connected' ? 'text-green-600 bg-green-100' : 
					connectionStatus === 'reconnecting' ? 'text-yellow-600 bg-yellow-100' : 
					'text-red-600 bg-red-100'
				}`}>
					{connectionStatus === 'connected' ? '🟢 Connected' : 
					 connectionStatus === 'reconnecting' ? '🟡 Reconnecting' : 
					 '🔴 Disconnected'}
				</span>
				{#if isPaused}
					<span class="text-yellow-600 bg-yellow-100 px-2 py-1 rounded">⏸️ Paused</span>
				{/if}
				<span class="text-gray-600">Updates: #{updateCounter}</span>
				{#if debugMode}
					<span class="text-purple-600 bg-purple-100 px-2 py-1 rounded">🐛 Debug ON</span>
				{/if}
			</div>
		{/if} -->
	</div>

	<!-- User Detail Sidebar -->
	<UserDetailSidebar
		bind:isOpen={isSidebarOpen}
		{selectedUserId}
		token={token || ''}
		on:close={closeSidebar}
		on:userUpdated={handleUserUpdated}
	/>
</div>

<!-- Enhanced Connection Status Toasts -->
<!-- {#if connectionStatus === 'reconnecting'}
	<div class="fixed top-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg shadow-lg z-50 animate-bounce">
		<div class="flex items-center gap-2">
			<Spinner class="w-5 h-5" />
			<div>
				<div class="text-sm font-medium">Reconnecting...</div>
				<div class="text-xs">Attempt {retryCount}/{maxRetries}</div>
			</div>
		</div>
	</div>
{:else if connectionStatus === 'disconnected'}
	<div class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
		<div class="flex items-center gap-2">
			<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
				<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
			</svg>
			<div>
				<div class="text-sm font-medium">Connection Lost</div>
				<button 
					on:click={() => {
						connectionStatus = 'connected';
						retryCount = 0;
						fetchUsersRealTime(true);
					}}
					class="text-xs underline hover:no-underline mt-1"
				>
					🔄 Retry Now
				</button>
			</div>
		</div>
	</div>
{/if} -->

<!-- Tab Visibility Warning -->
<!-- {#if isRealTimeEnabled && !isTabVisible}
	<div class="fixed top-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg shadow-lg z-50">
		<div class="flex items-center gap-2">
			<svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
				<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
			</svg>
			<div>
				<div class="text-sm font-medium">Updates Paused</div>
				<div class="text-xs">Tab not active - will resume when you return</div>
			</div>
		</div>
	</div>
{/if} -->

<style>
	.break-words {
		word-break: break-word;
		white-space: normal;
	}
</style>